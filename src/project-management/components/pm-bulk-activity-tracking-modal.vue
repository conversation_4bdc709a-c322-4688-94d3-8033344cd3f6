<script setup>
import dayjs from 'dayjs';

const emit = defineEmits(['close']);

const $t = inject('$t');

const state = reactive({
  interval: 'weekly',
  range: 'this_week',
  hot_data: [],
});

const interval_options = [
  ['daily', 'Daily'],
  ['weekly', 'Weekly'],
].map(item => ({
  value: item[0],
  label: $t(item[1]),
}));

const range_options = [
  ['this_week', 'This week'],
  ['this_month', 'This month'],
  ['last_7_days', 'Last 7 days'],
  ['last_14_days', 'Last 14 days'],
  ['last_30_days', 'Last 30 days'],
].map(item => ({
  value: item[0],
  label: $t(item[1]),
}));

const hot_columns = computed(() => {
  // dates are nested headers. Each date has 4 children - work done, amount spent, duration, items
  const dates = getDatesBetween(dayjs().startOf('month'), dayjs().endOf('month')).map(date => ({
    data: date,
    header: date,
    children: [
      {
        data: `${date}_work_done`,
        header: $t('Work done'),
      },
      {
        data: `${date}_amount_spent`,
        header: $t('Amount spent'),
      },
      {
        data: `${date}_duration`,
        header: $t('Duration'),
      },
      {
        data: `${date}_items`,
        header: $t('Items'),
      },
    ],
  }));
  return [
    {
      data: 'id',
      header: $t('ID'),
    },
    {
      data: 'activity',
      header: $t('Activity'),
    },
    {
      data: 'resource',
      header: $t('Resource'),
    },
    ...dates,
  ];
});

const hot_nested_headers = computed(() => {
  return [
    [$t('Activity'), $t('Resource'), ...hot_columns.value.slice(3).map(column => column.header)],
  ];
});

function getDatesBetween(min_date, max_date) {
  const dates = [];
  let current_date = min_date || dayjs(min_date);
  const end_date = max_date || dayjs(max_date);
  while (current_date.isSameOrBefore(end_date)) {
    dates.push(current_date.format('DD-MMM-YY'));
    current_date = current_date.add(1, 'day');
  }
  return dates;
}
</script>

<template>
  <HawkModalContainer content_class="w-[80vw]">
    <HawkModalHeader @close="emit('close')">
      <template #title>
        {{ $t('Activity tracking') }}
      </template>
    </HawkModalHeader>
    <HawkModalContent>
      <div class="flex justify-between items-center mb-6">
        <div class="text-sm font-normal text-gray-900">
          {{ $t('Track work done, amount spent for the activity, or record for a particular resource.') }}
        </div>
        <div class="flex items-center gap-2">
          <HawkMenu
            position="fixed"
            :items="range_options"
            @select="state.range = $event.value"
          >
            <template #trigger="{ open }">
              <div class="flex items-center gap-1">
                <span class="text-xs font-normal text-gray-500">
                  {{ $t('Range') }}:
                </span>
                <span class="text-xs font-medium text-gray-900">
                  {{ range_options.find((option) => option.value === state.range)?.label }}
                </span>
                <IconHawkChevronUp v-if="open" />
                <IconHawkChevronDown v-else />
              </div>
            </template>
          </HawkMenu>
          <HawkMenu
            position="fixed"
            :items="interval_options"
            @select="state.interval = $event.value"
          >
            <template #trigger="{ open }">
              <div class="flex items-center gap-1">
                <span class="text-xs font-normal text-gray-500">
                  {{ $t('Interval') }}:
                </span>
                <span class="text-xs font-medium text-gray-900">
                  {{ interval_options.find((option) => option.value === state.interval)?.label }}
                </span>
                <IconHawkChevronUp v-if="open" />
                <IconHawkChevronDown v-else />
              </div>
            </template>
          </HawkMenu>
        </div>
      </div>
      <HawkHandsOnTable
        :hot-settings="{
          // rowHeaders,
          // afterChange,
          // beforeKeyDown,
          nestedRows: true,
          bindRowsWithHeaders: true,
          // nestedHeaders: hot_nested_headers,
          // cells: cellsConfiguration,
          fixedColumnsStart: 1,
          className: 'htMiddle',
          contextMenu: false,
          dropdownMenu: false,
          columnSorting: false,
          headerClassName: 'htCenter',
        }"
        :right-click-menu="{}"
        :data="state.hot_data"
        :columns="hot_columns"
        :columns-menu="{ items: {} }"
        height="100%"
        class="pm-excel-modal"
        @ready="onHandsOnTableReady"
      />
    </HawkModalContent>
    <HawkModalFooter>
      <template #right>
        <div class="flex justify-end w-full col-span-full">
          <HawkButton
            type="outlined"
            class="mr-4"
            @click="emit('close')"
          >
            {{ $t('Cancel') }}
          </HawkButton>
          <HawkButton
            color="primary"
            @click="emit('close')"
          >
            {{ $t('Save') }}
          </HawkButton>
        </div>
      </template>
    </HawkModalFooter>
  </HawkModalContainer>
</template>
